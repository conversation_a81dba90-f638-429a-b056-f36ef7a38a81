import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('GalleryFilter', () {
    test('should create with default values', () {
      const filter = GalleryFilter();

      expect(filter.sortBy, SortBy.dateDescending);
      expect(filter.qualityFilter, QualityFilter.all);
      expect(filter.dateRange, isNull);
      expect(filter.searchQuery, isNull);
    });

    test('should create with custom values', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final dateRange = DateRange(start: startDate, end: endDate);

      const filter = GalleryFilter(
        sortBy: SortBy.qualityDescending,
        qualityFilter: QualityFilter.highQuality,
        dateRange: dateRange,
        searchQuery: 'test search',
      );

      expect(filter.sortBy, SortBy.qualityDescending);
      expect(filter.qualityFilter, QualityFilter.highQuality);
      expect(filter.dateRange, dateRange);
      expect(filter.searchQuery, 'test search');
    });

    test('should support copyWith method', () {
      const originalFilter = GalleryFilter(
        sortBy: SortBy.dateAscending,
        qualityFilter: QualityFilter.lowQuality,
      );

      final updatedFilter = originalFilter.copyWith(
        sortBy: SortBy.qualityAscending,
        searchQuery: 'new search',
      );

      expect(updatedFilter.sortBy, SortBy.qualityAscending);
      expect(updatedFilter.qualityFilter, QualityFilter.lowQuality);
      expect(updatedFilter.searchQuery, 'new search');
      expect(updatedFilter.dateRange, isNull);
    });

    test('should support copyWith with null values', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final dateRange = DateRange(start: startDate, end: endDate);

      const originalFilter = GalleryFilter(
        sortBy: SortBy.dateDescending,
        qualityFilter: QualityFilter.highQuality,
        dateRange: dateRange,
        searchQuery: 'original search',
      );

      final updatedFilter = originalFilter.copyWith(
        dateRange: null,
        searchQuery: null,
      );

      expect(updatedFilter.dateRange, isNull);
      expect(updatedFilter.searchQuery, isNull);
      expect(updatedFilter.sortBy, SortBy.dateDescending);
      expect(updatedFilter.qualityFilter, QualityFilter.highQuality);
    });

    test('should support equality comparison', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final dateRange = DateRange(start: startDate, end: endDate);

      const filter1 = GalleryFilter(
        sortBy: SortBy.qualityDescending,
        qualityFilter: QualityFilter.mediumQuality,
        dateRange: dateRange,
        searchQuery: 'test',
      );

      const filter2 = GalleryFilter(
        sortBy: SortBy.qualityDescending,
        qualityFilter: QualityFilter.mediumQuality,
        dateRange: dateRange,
        searchQuery: 'test',
      );

      const filter3 = GalleryFilter(
        sortBy: SortBy.dateAscending,
        qualityFilter: QualityFilter.mediumQuality,
        dateRange: dateRange,
        searchQuery: 'test',
      );

      expect(filter1, equals(filter2));
      expect(filter1, isNot(equals(filter3)));
    });

    test('should have proper props for equality', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final dateRange = DateRange(start: startDate, end: endDate);

      const filter = GalleryFilter(
        sortBy: SortBy.qualityDescending,
        qualityFilter: QualityFilter.highQuality,
        dateRange: dateRange,
        searchQuery: 'test search',
      );

      expect(filter.props, [
        SortBy.qualityDescending,
        QualityFilter.highQuality,
        dateRange,
        'test search',
      ]);
    });

    group('SortBy enum', () {
      test('should have all expected values', () {
        expect(SortBy.values, [
          SortBy.dateDescending,
          SortBy.dateAscending,
          SortBy.qualityDescending,
          SortBy.qualityAscending,
        ]);
      });
    });

    group('QualityFilter enum', () {
      test('should have all expected values', () {
        expect(QualityFilter.values, [
          QualityFilter.all,
          QualityFilter.highQuality,
          QualityFilter.mediumQuality,
          QualityFilter.lowQuality,
        ]);
      });
    });
  });

  group('DateRange', () {
    test('should create with start and end dates', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final dateRange = DateRange(start: startDate, end: endDate);

      expect(dateRange.start, startDate);
      expect(dateRange.end, endDate);
    });

    test('should support equality comparison', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);

      final dateRange1 = DateRange(start: startDate, end: endDate);
      final dateRange2 = DateRange(start: startDate, end: endDate);
      final dateRange3 = DateRange(
        start: startDate,
        end: DateTime(2024, 2, 28),
      );

      expect(dateRange1, equals(dateRange2));
      expect(dateRange1, isNot(equals(dateRange3)));
    });

    test('should have proper props for equality', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 1, 31);
      final dateRange = DateRange(start: startDate, end: endDate);

      expect(dateRange.props, [startDate, endDate]);
    });

    test('should handle same start and end date', () {
      final sameDate = DateTime(2024, 1, 15);
      final dateRange = DateRange(start: sameDate, end: sameDate);

      expect(dateRange.start, sameDate);
      expect(dateRange.end, sameDate);
    });

    test('should handle edge case dates', () {
      final minDate = DateTime(1970, 1, 1);
      final maxDate = DateTime(2100, 12, 31);
      final dateRange = DateRange(start: minDate, end: maxDate);

      expect(dateRange.start, minDate);
      expect(dateRange.end, maxDate);
    });

    test('should allow end date before start date', () {
      // Note: The model doesn't enforce date order validation
      final startDate = DateTime(2024, 1, 31);
      final endDate = DateTime(2024, 1, 1);
      final dateRange = DateRange(start: startDate, end: endDate);

      expect(dateRange.start, startDate);
      expect(dateRange.end, endDate);
    });
  });
}
