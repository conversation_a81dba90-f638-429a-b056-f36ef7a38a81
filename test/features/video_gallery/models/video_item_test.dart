import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoItem', () {
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 15, 10, 30, 45);
    });

    test('should create with all required parameters', () {
      const videoItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
      );

      expect(videoItem.id, 'test-id-123');
      expect(videoItem.fileName, 'face_verification_2024-01-15_10-30-45.mp4');
      expect(videoItem.filePath, '/path/to/video.mp4');
      expect(videoItem.fileSize, 1024000);
      expect(videoItem.createdAt, testDate);
      expect(videoItem.qualityScore, 85.5);
      expect(videoItem.thumbnailPath, isNull);
      expect(videoItem.duration, isNull);
    });

    test('should create with optional parameters', () {
      const videoItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
        thumbnailPath: '/path/to/thumbnail.jpg',
        duration: Duration(seconds: 9),
      );

      expect(videoItem.thumbnailPath, '/path/to/thumbnail.jpg');
      expect(videoItem.duration, const Duration(seconds: 9));
    });

    test('should support copyWith method', () {
      const originalItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
      );

      final updatedItem = originalItem.copyWith(
        thumbnailPath: '/path/to/thumbnail.jpg',
        duration: const Duration(seconds: 9),
        qualityScore: 90.0,
      );

      expect(updatedItem.id, originalItem.id);
      expect(updatedItem.fileName, originalItem.fileName);
      expect(updatedItem.filePath, originalItem.filePath);
      expect(updatedItem.fileSize, originalItem.fileSize);
      expect(updatedItem.createdAt, originalItem.createdAt);
      expect(updatedItem.thumbnailPath, '/path/to/thumbnail.jpg');
      expect(updatedItem.duration, const Duration(seconds: 9));
      expect(updatedItem.qualityScore, 90.0);
    });

    test('should support copyWith with null values', () {
      const originalItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
        thumbnailPath: '/path/to/thumbnail.jpg',
        duration: Duration(seconds: 9),
      );

      final updatedItem = originalItem.copyWith(
        thumbnailPath: null,
        duration: null,
      );

      expect(updatedItem.thumbnailPath, isNull);
      expect(updatedItem.duration, isNull);
      expect(updatedItem.qualityScore, originalItem.qualityScore);
    });

    test('should support equality comparison', () {
      const item1 = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
      );

      const item2 = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
      );

      const item3 = VideoItem(
        id: 'different-id',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
      );

      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
    });

    test('should have proper props for equality', () {
      const videoItem = VideoItem(
        id: 'test-id-123',
        fileName: 'face_verification_2024-01-15_10-30-45.mp4',
        filePath: '/path/to/video.mp4',
        fileSize: 1024000,
        createdAt: testDate,
        qualityScore: 85.5,
        thumbnailPath: '/path/to/thumbnail.jpg',
        duration: Duration(seconds: 9),
      );

      expect(videoItem.props, [
        'test-id-123',
        'face_verification_2024-01-15_10-30-45.mp4',
        '/path/to/video.mp4',
        1024000,
        testDate,
        85.5,
        '/path/to/thumbnail.jpg',
        const Duration(seconds: 9),
      ]);
    });

    test('should handle edge case values', () {
      final futureDate = DateTime(2030, 12, 31, 23, 59, 59);
      
      const videoItem = VideoItem(
        id: '',
        fileName: '',
        filePath: '',
        fileSize: 0,
        createdAt: futureDate,
        qualityScore: 0.0,
      );

      expect(videoItem.id, '');
      expect(videoItem.fileName, '');
      expect(videoItem.filePath, '');
      expect(videoItem.fileSize, 0);
      expect(videoItem.createdAt, futureDate);
      expect(videoItem.qualityScore, 0.0);
    });

    test('should handle maximum values', () {
      final maxDate = DateTime(9999, 12, 31, 23, 59, 59);
      
      const videoItem = VideoItem(
        id: 'very-long-id-with-many-characters-to-test-edge-cases',
        fileName: 'very_long_file_name_with_many_characters_to_test_edge_cases.mp4',
        filePath: '/very/long/path/to/test/edge/cases/video.mp4',
        fileSize: 9223372036854775807, // Max int64
        createdAt: maxDate,
        qualityScore: 100.0,
        duration: Duration(hours: 24),
      );

      expect(videoItem.fileSize, 9223372036854775807);
      expect(videoItem.qualityScore, 100.0);
      expect(videoItem.duration, const Duration(hours: 24));
    });

    test('should handle negative quality score', () {
      const videoItem = VideoItem(
        id: 'test-id',
        fileName: 'test.mp4',
        filePath: '/path/test.mp4',
        fileSize: 1000,
        createdAt: testDate,
        qualityScore: -10.0,
      );

      expect(videoItem.qualityScore, -10.0);
    });

    test('should handle very small file size', () {
      const videoItem = VideoItem(
        id: 'test-id',
        fileName: 'tiny.mp4',
        filePath: '/path/tiny.mp4',
        fileSize: 1,
        createdAt: testDate,
        qualityScore: 50.0,
      );

      expect(videoItem.fileSize, 1);
    });

    test('should handle zero duration', () {
      const videoItem = VideoItem(
        id: 'test-id',
        fileName: 'instant.mp4',
        filePath: '/path/instant.mp4',
        fileSize: 1000,
        createdAt: testDate,
        qualityScore: 75.0,
        duration: Duration.zero,
      );

      expect(videoItem.duration, Duration.zero);
    });
  });
}
